#!/usr/bin/env python3
"""
Merge all DOCX files into a single EPUB with proper chapter structure and images.
Two-step approach: DOCX -> HTML (with images) -> merge -> EPUB
Optimized version with proper chapter hierarchy and code block handling.
"""
import os
import subprocess
from pathlib import Path
import shutil
import re
from bs4 import BeautifulSoup

# Define the correct order of documents with hierarchy levels
document_order = [
    # Front matter (level 0 - no chapter numbering)
    {"file": "Agentic Design Patterns.docx", "level": 0, "title": "Title Page"},
    {"file": "Foreword.docx", "level": 0, "title": "Foreword"},
    {"file": "Acknowledgment.docx", "level": 0, "title": "Acknowledgment"},
    {"file": "A Thought Leader's Perspective_ Power and Responsibility.docx", "level": 0, "title": "A Thought Leader's Perspective: Power and Responsibility"},
    {"file": "Introduction.docx", "level": 0, "title": "Introduction"},
    {"file": "What makes an AI system an Agent_.docx", "level": 0, "title": "What makes an AI system an Agent?"},

    # Main chapters (level 1)
    {"file": "Chapter 1_ Prompt Chaining.docx", "level": 1, "title": "Chapter 1: Prompt Chaining"},
    {"file": "Chapter 2_ Routing.docx", "level": 1, "title": "Chapter 2: Routing"},
    {"file": "Chapter 3_ Parallelization.docx", "level": 1, "title": "Chapter 3: Parallelization"},
    {"file": "Chapter 4_ Reflection.docx", "level": 1, "title": "Chapter 4: Reflection"},
    {"file": "Chapter 5_ Tool Use.docx", "level": 1, "title": "Chapter 5: Tool Use"},
    {"file": "Chapter 6_ Planning.docx", "level": 1, "title": "Chapter 6: Planning"},
    {"file": "Chapter 7_ Multi-Agent Collaboration.docx", "level": 1, "title": "Chapter 7: Multi-Agent Collaboration"},
    {"file": "Chapter 8_ Memory Management.docx", "level": 1, "title": "Chapter 8: Memory Management"},
    {"file": "Chapter 9_ Learning and Adaptation.docx", "level": 1, "title": "Chapter 9: Learning and Adaptation"},
    {"file": "Chapter 10_ Model Context Protocol (MCP).docx", "level": 1, "title": "Chapter 10: Model Context Protocol (MCP)"},
    {"file": "Chapter 11_ Goal Setting and Monitoring.docx", "level": 1, "title": "Chapter 11: Goal Setting and Monitoring"},
    {"file": "Chapter 12_ Exception Handling and Recovery.docx", "level": 1, "title": "Chapter 12: Exception Handling and Recovery"},
    {"file": "Chapter 13_ Human-in-the-Loop.docx", "level": 1, "title": "Chapter 13: Human-in-the-Loop"},
    {"file": "Chapter 14_ Knowledge Retrieval (RAG).docx", "level": 1, "title": "Chapter 14: Knowledge Retrieval (RAG)"},
    {"file": "Chapter 15_ Inter-Agent Communication (A2A).docx", "level": 1, "title": "Chapter 15: Inter-Agent Communication (A2A)"},
    {"file": "Chapter 16_ Resource-Aware Optimization.docx", "level": 1, "title": "Chapter 16: Resource-Aware Optimization"},
    {"file": "Chapter 17_ Reasoning Techniques.docx", "level": 1, "title": "Chapter 17: Reasoning Techniques"},
    {"file": "Chapter 18_ Guardrails_Safety Patterns.docx", "level": 1, "title": "Chapter 18: Guardrails/Safety Patterns"},
    {"file": "Chapter 19_ Evaluation and Monitoring.docx", "level": 1, "title": "Chapter 19: Evaluation and Monitoring"},
    {"file": "Chapter 20_ Prioritization.docx", "level": 1, "title": "Chapter 20: Prioritization"},
    {"file": "Chapter 21_ Exploration and Discovery.docx", "level": 1, "title": "Chapter 21: Exploration and Discovery"},

    # Back matter (level 0)
    {"file": "Conclusion.docx", "level": 0, "title": "Conclusion"},

    # Appendices (level 1)
    {"file": "Appendix A_ Advanced Prompting Techniques.docx", "level": 1, "title": "Appendix A: Advanced Prompting Techniques"},
    {"file": "Appendix B - AI Agentic Interactions_ From GUI to Real world environment.docx", "level": 1, "title": "Appendix B: AI Agentic Interactions - From GUI to Real world environment"},
    {"file": "Appendix C - Quick overview of Agentic Frameworks.docx", "level": 1, "title": "Appendix C: Quick overview of Agentic Frameworks"},
    {"file": "Appendix D - Building an Agent with AgentSpace (on-line only).docx", "level": 1, "title": "Appendix D: Building an Agent with AgentSpace (on-line only)"},
    {"file": "Appendix E - AI Agents on the CLI.docx", "level": 1, "title": "Appendix E: AI Agents on the CLI"},
    {"file": "Appendix F  - Under the Hood_ An Inside Look at the Agents' Reasoning Engines.docx", "level": 1, "title": "Appendix F: Under the Hood - An Inside Look at the Agents' Reasoning Engines"},
    {"file": "Appendix G -  Coding agents.docx", "level": 1, "title": "Appendix G: Coding agents"},

    # Reference materials (level 0)
    {"file": "Glossary.docx", "level": 0, "title": "Glossary"},
    {"file": "Index of Terms.docx", "level": 0, "title": "Index of Terms"},
]

def fix_code_blocks_in_html(html_file):
    """
    Fix code blocks that were incorrectly converted to tables.
    Convert table-based code blocks back to proper <pre><code> blocks.
    """
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()

        soup = BeautifulSoup(content, 'html.parser')

        # Find tables that contain code (usually single column tables with code-like content)
        tables = soup.find_all('table')
        for table in tables:
            # Check if this table looks like a code block
            rows = table.find_all('tr')
            if len(rows) == 1:  # Single row table
                cell = rows[0].find('td') or rows[0].find('th')
                if cell:
                    cell_text = cell.get_text().strip()
                    # Check if content looks like code (contains common code patterns)
                    if (any(keyword in cell_text for keyword in ['import ', 'def ', 'class ', 'function', '()', '{', '}', ';', 'pip install', 'npm install']) or
                        len(cell_text.split('\n')) > 1 and not cell_text.startswith('Note:')):

                        # Convert table to code block
                        code_block = soup.new_tag('pre')
                        code_tag = soup.new_tag('code')
                        code_tag.string = cell_text
                        code_block.append(code_tag)

                        # Replace table with code block
                        table.replace_with(code_block)

        # Save the modified HTML
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(str(soup))

        return True
    except Exception as e:
        print(f"  Warning: Could not fix code blocks in {html_file}: {e}")
        return False

def add_chapter_headers(html_file, doc_info, index):
    """
    Add proper chapter headers to HTML files based on hierarchy level.
    """
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()

        soup = BeautifulSoup(content, 'html.parser')

        # Find the body or main content area
        body = soup.find('body') or soup

        # Create chapter header based on level
        level = doc_info['level']
        title = doc_info['title']

        if level == 1:  # Main chapters and appendices
            header_tag = soup.new_tag('h1')
            header_tag['class'] = 'chapter-title'
        else:  # Front matter, back matter
            header_tag = soup.new_tag('h1')
            header_tag['class'] = 'section-title'

        header_tag.string = title

        # Insert header at the beginning of body content
        if body.contents:
            body.insert(0, header_tag)
        else:
            body.append(header_tag)

        # Save the modified HTML
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(str(soup))

        return True
    except Exception as e:
        print(f"  Warning: Could not add chapter header to {html_file}: {e}")
        return False

def main():
    base_dir = Path(__file__).parent
    temp_dir = base_dir / "temp_conversion"
    output_file = base_dir / "Agentic_Design_Patterns_Complete_Optimized.epub"

    # Clean up temp directory if it exists
    if temp_dir.exists():
        shutil.rmtree(temp_dir)
    temp_dir.mkdir()

    # Check which files exist and convert each to HTML with embedded images
    existing_files = []

    print("Step 1: Converting DOCX files to HTML with images...")
    for i, doc_info in enumerate(document_order, 1):
        doc_path = base_dir / doc_info['file']
        if doc_path.exists():
            html_file = temp_dir / f"{i:03d}_{doc_path.stem}.html"
            media_dir = temp_dir / f"{i:03d}_{doc_path.stem}_files"

            # Use pandoc to convert DOCX to HTML with extracted media
            cmd = [
                "pandoc",
                str(doc_path),
                "-o", str(html_file),
                "--extract-media", str(media_dir),
                "--standalone",
            ]

            try:
                subprocess.run(cmd, check=True, capture_output=True)

                # Post-process the HTML file
                print(f"  ✓ {i:2d}. {doc_info['file']} -> Processing...")

                # Fix code blocks that were converted to tables
                fix_code_blocks_in_html(html_file)

                # Add proper chapter headers
                add_chapter_headers(html_file, doc_info, i)

                existing_files.append(str(html_file))
                print(f"      Processed: {doc_info['title']}")

            except subprocess.CalledProcessError as e:
                print(f"  ✗ Failed to convert: {doc_info['file']}")
                print(f"     Error: {e.stderr.decode()}")
        else:
            print(f"  - Missing: {doc_info['file']}")
    
    print(f"\nStep 2: Merging {len(existing_files)} HTML files into EPUB...")

    # Create a custom CSS file for better styling
    css_file = temp_dir / "custom.css"
    css_content = """
/* Custom styles for the EPUB */
.chapter-title {
    font-size: 1.5em;
    font-weight: bold;
    margin-top: 2em;
    margin-bottom: 1em;
    page-break-before: always;
    border-bottom: 2px solid #333;
    padding-bottom: 0.5em;
}

.section-title {
    font-size: 1.3em;
    font-weight: bold;
    margin-top: 1.5em;
    margin-bottom: 1em;
    page-break-before: always;
}

pre {
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 1em;
    margin: 1em 0;
    overflow-x: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
    line-height: 1.4;
}

code {
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
    background-color: #f5f5f5;
    padding: 0.2em 0.4em;
    border-radius: 3px;
}

pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
}

table {
    border-collapse: collapse;
    width: 100%;
    margin: 1em 0;
}

th, td {
    border: 1px solid #ddd;
    padding: 0.5em;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}
"""

    with open(css_file, 'w', encoding='utf-8') as f:
        f.write(css_content)

    # Now merge all HTML files into one EPUB with improved settings
    cmd = [
        "pandoc",
        *existing_files,
        "-o", str(output_file),
        "--toc",
        "--toc-depth=3",
        "--epub-chapter-level=1",
        "--css", str(css_file),
        "--metadata", "title=Agentic Design Patterns",
        "--metadata", "author=Compiled Edition",
        "--metadata", f"date=2025",
        "--metadata", "language=en-US",
        "--standalone",
        "--wrap=preserve",
    ]

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print(f"\n✓ Successfully created: {output_file.name}")

        # Get file size
        size_mb = output_file.stat().st_size / (1024 * 1024)
        print(f"  File size: {size_mb:.2f} MB")

        # Clean up temp directory
        print(f"\nStep 3: Cleaning up temporary files...")
        shutil.rmtree(temp_dir)
        print(f"  ✓ Removed temp directory")

        print(f"\n🎉 Optimization complete!")
        print(f"   - Fixed chapter hierarchy levels")
        print(f"   - Converted table-based code blocks to proper <pre><code> blocks")
        print(f"   - Added custom CSS for better formatting")
        print(f"   - Enhanced table of contents structure")

        return True
    except subprocess.CalledProcessError as e:
        print(f"\n✗ Error during conversion:")
        print(e.stderr)
        return False

if __name__ == "__main__":
    main()
