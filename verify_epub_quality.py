#!/usr/bin/env python3
"""
Quick verification script for EPUB quality.
Checks for common issues in the generated EPUB file.
"""
import os
import subprocess
import tempfile
import shutil
from pathlib import Path
from bs4 import BeautifulSoup

def verify_epub_quality(epub_file):
    """Verify the quality of the generated EPUB file."""
    
    print(f"🔍 Verifying EPUB quality: {epub_file}")
    
    if not Path(epub_file).exists():
        print(f"❌ EPUB file not found: {epub_file}")
        return False
    
    # Create temporary directory for extraction
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Extract EPUB
        try:
            subprocess.run(['unzip', '-q', epub_file, '-d', str(temp_path)], 
                         check=True, capture_output=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to extract EPUB: {e}")
            return False
        
        epub_dir = temp_path / "EPUB"
        text_dir = epub_dir / "text"
        
        if not text_dir.exists():
            print(f"❌ No text directory found in EPUB")
            return False
        
        # Check 1: Verify chapter titles
        print("\n📖 Checking chapter titles...")
        chapter_files = sorted(text_dir.glob("ch*.xhtml"))
        chapter_count = 0
        section_count = 0
        
        for file in chapter_files:
            with open(file, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')
                
            chapter_titles = soup.find_all('h1', class_='chapter-title')
            section_titles = soup.find_all('h1', class_='section-title')
            
            if chapter_titles:
                chapter_count += len(chapter_titles)
                for title in chapter_titles:
                    print(f"  ✅ Chapter: {title.get_text().strip()}")
            
            if section_titles:
                section_count += len(section_titles)
                for title in section_titles:
                    print(f"  ✅ Section: {title.get_text().strip()}")
        
        print(f"  📊 Found {chapter_count} chapters and {section_count} sections")
        
        # Check 2: Verify code blocks
        print("\n💻 Checking code blocks...")
        code_block_count = 0
        table_code_count = 0
        
        for file in chapter_files:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
                soup = BeautifulSoup(content, 'html.parser')
            
            # Count proper code blocks
            code_blocks = soup.find_all('pre')
            code_block_count += len(code_blocks)
            
            # Check for remaining table-based code (should be 0)
            tables = soup.find_all('table')
            for table in tables:
                table_text = table.get_text()
                if any(keyword in table_text for keyword in ['import ', 'def ', 'pip install']):
                    table_code_count += 1
                    print(f"  ⚠️  Found table-based code in {file.name}")
        
        print(f"  📊 Found {code_block_count} proper code blocks")
        if table_code_count == 0:
            print(f"  ✅ No table-based code blocks found")
        else:
            print(f"  ❌ Found {table_code_count} table-based code blocks")
        
        # Check 3: Verify problematic headings
        print("\n🔍 Checking for problematic headings...")
        long_heading_count = 0
        
        for file in chapter_files:
            with open(file, 'r', encoding='utf-8') as f:
                soup = BeautifulSoup(f.read(), 'html.parser')
            
            headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
            for heading in headings:
                heading_text = heading.get_text().strip()
                if len(heading_text) > 200 or 'Critic-Reviewer:' in heading_text:
                    long_heading_count += 1
                    print(f"  ❌ Long heading in {file.name}: {heading_text[:100]}...")
        
        if long_heading_count == 0:
            print(f"  ✅ No problematic headings found")
        else:
            print(f"  ❌ Found {long_heading_count} problematic headings")
        
        # Check 4: Verify CSS
        print("\n🎨 Checking CSS...")
        css_files = list((epub_dir / "styles").glob("*.css"))
        if css_files:
            css_file = css_files[0]
            with open(css_file, 'r', encoding='utf-8') as f:
                css_content = f.read()
            
            required_styles = ['.chapter-title', '.section-title', 'pre', 'code']
            missing_styles = []
            
            for style in required_styles:
                if style not in css_content:
                    missing_styles.append(style)
            
            if not missing_styles:
                print(f"  ✅ All required CSS styles found")
            else:
                print(f"  ❌ Missing CSS styles: {missing_styles}")
        else:
            print(f"  ❌ No CSS files found")
        
        # Summary
        print(f"\n📋 Summary:")
        print(f"  • Chapters: {chapter_count}")
        print(f"  • Sections: {section_count}")
        print(f"  • Code blocks: {code_block_count}")
        print(f"  • Table-based code: {table_code_count}")
        print(f"  • Problematic headings: {long_heading_count}")
        
        # Overall assessment
        issues = table_code_count + long_heading_count
        if issues == 0:
            print(f"\n🎉 EPUB quality check PASSED! No issues found.")
            return True
        else:
            print(f"\n⚠️  EPUB quality check found {issues} issues.")
            return False

if __name__ == "__main__":
    epub_file = "Agentic_Design_Patterns_Complete_Optimized.epub"
    verify_epub_quality(epub_file)
